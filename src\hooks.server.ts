import { sequence } from '@sveltejs/kit/hooks';
import { redirect, type <PERSON>le } from '@sveltejs/kit';
import { paraglideMiddleware } from '$lib/paraglide/server';
import { validateSession } from '$lib/server/auth/auth.service';

const handleAuth: Handle = async ({ event, resolve }) => {
	if (event.route.id?.startsWith('/(dashboard)/')) {
		const session = event.cookies.get('session');
		// If session exists, try to get user
		if (session) {
			event.locals.user = await getUserFromSession(session);
			console.log('Event locals user: Hooks server: ', event.locals.user);
		} else {
			event.locals.user = null;
			return redirect(302, '/login');
		}
	}

	return await resolve(event);
};

const handleParaglide: Handle = async ({ event, resolve }) =>
	await paraglideMiddleware(event.request, ({ request, locale }) => {
		event.request = request;
		return resolve(event, {
			transformPageChunk: ({ html }) => html.replace('%paraglide.lang%', locale)
		});
	});

export const handle: Handle = sequence(handleAuth, handleParaglide);

async function getUserFromSession(session: string | undefined) {
	if (!session) {
		return null;
	}

	const result = await validateSession(session);
	return result.success && result.data ? result.data : null;
}
