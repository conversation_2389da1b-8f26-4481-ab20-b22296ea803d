import { Hono } from 'hono';
import { tasksRouter } from './tasks';
import { authRouter } from './auth';
import { trackerRouter } from './tracker';

import { upgradeWebSocket, websocket } from '$lib/server/websocket';

const app = new Hono()
	.route('/tasks', tasksRouter)
	.route('/auth', authRouter)
	.route('/tracker', trackerRouter);

export const api = new Hono().route('/api', app);

export type Router = typeof app;

// @ts-expect-error
if (!globalThis.__bun_server__) {
	// @ts-expect-error
	globalThis.__bun_server__ = Bun.serve({
		port: 4000,
		fetch: api.fetch,
		websocket,
	});
}
