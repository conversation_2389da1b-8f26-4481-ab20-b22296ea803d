import { authMiddleware } from '$lib/server/auth/auth.middleware';
import type { AuthResponse } from '$lib/server/auth/auth.types';
import { WSCommands } from '$lib/types/wsCommands';
import {
	createActiveTimer,
	createTimeEntry,
	getActiveTimerForUser,
	getAllTimeEntriesForUser,
	stopUserTimer
} from '$lib/server/time-entries/time-entries.service';
import { zValidator } from '@hono/zod-validator';
import { Hono } from 'hono';
import { z } from 'zod';
import { client as redisClient } from '$lib/server/db/redis-client';
import { upgradeWebSocket } from '$lib/server/websocket';
import type { ServerWebSocket } from 'bun';
const topic = 'time-tracker';

export const trackerRouter = new Hono<{ Variables: { authUser: AuthResponse['data'] } }>()
	.use('*', authMiddleware)
	.get('/', async (c) => {
		const authUser = c.get('authUser');
		const authUserId = authUser?.id;

		// Parse query params for filter, sort, pagination
		const query = c.req.query();
		const page = parseInt(query.page) || 1;
		const pageSize = parseInt(query.pageSize) || 10;
		const sort = query.sort || 'created_at:desc';

		// Parse filters with operators
		const filters = [];
		for (const [key, value] of Object.entries(query)) {
			if (key !== 'page' && key !== 'limit' && key !== 'sort') {
				const [field, operator = 'eq'] = key.split('__'); // Default to 'eq' if no operator
				if (operator === 'between') {
					const [start, end] = value.split(',');
					filters.push({ field, operator, value: [start, end] });
				} else {
					filters.push({ field, operator, value });
				}
			}
		}

		const { entries, total } = await getAllTimeEntriesForUser(
			authUserId,
			page,
			pageSize,
			sort,
			filters
		);

		return c.json({ data: entries, total, randomNumber: Math.random() });
	})
	.post('/', async (c) => {
		// Get authUserId from authMiddleware
		const authUser = c.get('authUser');
		const authUserId = authUser?.id;

		const createdTimeEntry = await createTimeEntry(
			authUserId,
			new Date(),
			new Date('2025-07-14T12:00:00Z'),
			`Description ${Math.random().toPrecision(3)}`,
			['tag 1', 'tag 2']
		);
		return c.json(createdTimeEntry);
	})
	.get('/start', async (c) => {
		// Get authUserId from authMiddleware
		const authUser = c.get('authUser');
		const authUserId = authUser?.id;

		const activeTimer = await getActiveTimerForUser(authUserId);
		if (activeTimer) {
			return c.json(
				{
					message: 'User already has an active timer running. Please stop the current timer first.'
				},
				500
			);
		}

		const createdTimeEntry = await createTimeEntry(
			authUserId,
			new Date(),
			undefined,
			`Timer ${Math.random().toPrecision(3)}`
		);

		if (!createdTimeEntry) {
			// Return error
			return c.json({ message: 'Failed to create time entry' }, 500);
		}

		const createdActiveTimer = await createActiveTimer(authUserId, createdTimeEntry.id);

		// @ts-expect-error
		globalThis.__bun_server__.publish(
			authUserId,
			JSON.stringify({
				command: WSCommands.startTimer,
				data: createdTimeEntry
			})
		);

		return c.json(createdActiveTimer);
	})
	.get('/stop', async (c) => {
		// Get authUserId from authMiddleware
		const authUser = c.get('authUser');
		const authUserId = authUser?.id;
		let endedTimeEntry: Promise<any> | undefined = undefined;

		try {
			endedTimeEntry = await stopUserTimer(authUserId);
		} catch (err) {
			console.log(err);
		} finally {
			// @ts-expect-error
			globalThis.__bun_server__.publish(
				authUserId,
				JSON.stringify({
					command: WSCommands.stopTimer
				})
			);

			return c.json(endedTimeEntry || { message: 'Timer stopped' });
		}
	});

trackerRouter.get(
	'/ws',
	upgradeWebSocket((c) => {
		const authUser = c.get('authUser');
		const authUserId = authUser?.id;

		return {
			onOpen: async (_, ws) => {
				const rawWs = ws.raw as ServerWebSocket;
				rawWs.subscribe(authUserId);

				const activeTimer = await getActiveTimerForUser(authUserId);
				if (activeTimer) {
					// @ts-expect-error
					globalThis.__bun_server__.publish(
						authUserId,
						JSON.stringify({
							command: WSCommands.startTimer,
							data: activeTimer.time_entry
						})
					);
				}
				console.log(`WebSocket server opened and subscribed to topic '${authUserId}'`);
			},
			onClose(_, ws) {
				const rawWs = ws.raw as ServerWebSocket;
				rawWs.unsubscribe(authUserId);
				console.log(`WebSocket server closed and unsubscribed from topic '${authUserId}'`);
			}
		};
	})
);
// .post('/:id/finish', zValidator('param', TaskParam), (c) => {
// 	const { id } = c.req.valid('param');
// 	const task = tasks.find((task) => task.id === id);
// 	if (task) {
// 		task.done = true;
// 		return c.json(task);
// 	}

// 	throw c.json({ message: 'Task not found' }, 404);
// })
