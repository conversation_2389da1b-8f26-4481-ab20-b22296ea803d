<script lang="ts">
	import Header from '$lib/components/Header.svelte';

	import { makeClient } from '$lib/make-client.js';
	import { onMount } from 'svelte';
	import { WSCommands } from '$lib/types/wsCommands';

	const client = makeClient(fetch);

	let fetchPromise = $state<Promise<any>>(Promise.resolve({}));
	let currentController: AbortController | null = null;
	let timeEntries = $state<any[]>([]);
	let activeTimerEntry = $state<any>(null);
	let timerInterval: NodeJS.Timeout | null = null;
	let timerTotalTime = $state<number>(0);
	let ws: WebSocket;

	async function getTimeEntries() {
		// Cancel previous request if it's still pending
		if (currentController) {
			currentController.abort();
		}

		// Create new abort controller for this request
		currentController = new AbortController();

		// Set the promise immediately so the UI shows loading state
		fetchPromise = (async () => {
			try {
				const timeEntriesData = await client.tracker.$get(
					{},
					{
						init: {
							signal: currentController.signal
						}
					}
				);

				if (!timeEntriesData.ok) {
					throw new Error('Failed to fetch time entries');
				}

				timeEntries = [await timeEntriesData.json()];

				return timeEntries;
			} catch (error) {
				// Don't throw if the request was aborted
				if (error instanceof Error && error.name === 'AbortError') {
					console.log('Request was cancelled');
					return null; // Return null for cancelled requests
				}
				throw error;
			}
		})();

		return fetchPromise;
	}

	async function createTimeEntry() {
		const timeEntriesData = await client.tracker.$get();
		return timeEntries.push(await timeEntriesData.json());
	}

	async function startTimer(activeTimerData: any = null) {
		if (!activeTimerData) {
			await client.tracker.start.$get();
		} else {
			activeTimerEntry = activeTimerData;
			timerTotalTime = +new Date() - +new Date(activeTimerEntry.start_time);
			console.log(timerTotalTime)
			timerInterval = setInterval(() => {
				timerTotalTime += 1;
			}, 1000);
		}
	}

	function stopCountTimer() {
		if (timerInterval) {
			clearInterval(timerInterval);
		}
		timerTotalTime = 0;
		return (activeTimerEntry = null);
	}

	async function stopTimer() {
		const endedTimeEntry = await client.tracker.stop.$get();
		timeEntries.push(await endedTimeEntry.json());
	}

	function connectWebSocket() {
		ws = new WebSocket('ws://localhost:4000/api/tracker/ws');
		ws.onopen = () => {
			console.log('WebSocket opened');
		};
		ws.onmessage = async (event) => {
			const wsPayload = JSON.parse(event.data);
			console.log('Received: ', wsPayload);

			console.log('wsPayload.data - ', wsPayload.data);

			switch (wsPayload.command as WSCommands) {
				case WSCommands.startTimer:
					startTimer(wsPayload.data);
					console.log('Websocket client callback Timer started');
					break;

				case WSCommands.stopTimer:
					stopCountTimer();
					await getTimeEntries();
					console.log('Websocket client callback Timer stop');
					break;

				default:
					break;
			}
		};
	}

	onMount(async () => {
		await getTimeEntries();
		connectWebSocket();

		setTimeout(() => {
			console.log(timerInterval);
		}, 1000);
	});
</script>

<div class="container">
	<Header user={{ name: 'Michael' }} />

	<!-- <div class="">
		<button class="bg-yellow-200 p-4" onclick={connectWebSocket}>Connect WS</button>
	</div> -->

	<div class="flex gap-4">
		{#if timerTotalTime > 0}
			<!-- Counter -->
			<div class="flex flex-col gap-2">
				<!-- Seconds to time format -->
				<div class="text-2xl font-bold">
					{Math.floor(timerTotalTime / 60)
						.toString()
						.padStart(2, '0')}:{(timerTotalTime % 60).toString().padStart(2, '0')}
				</div>
			</div>

			<pre>{JSON.stringify(activeTimerEntry, null, 2)}</pre>
		{/if}
		<div class="">
			{#if timerTotalTime > 0}
				<button class="bg-red-200 p-4" onclick={() => stopTimer()}>Stop Timer</button>
			{:else}
				<button class="bg-green-200 p-4" onclick={() => startTimer()}>Start Timer</button>
			{/if}
		</div>
	</div>

	<button class="bg-green-200 p-4" onclick={createTimeEntry}>createTimeEntry</button>
	<button class="bg-blue-200 p-4" onclick={getTimeEntries}>Refresh Time Entries</button>

	{#await fetchPromise}
		<p>Loading...</p>
	{:then data}
		{#if data !== null}
			<pre>{JSON.stringify(timeEntries, null, 2)}</pre>
		{:else}
			<p>Request was cancelled</p>
		{/if}
	{:catch error}
		<p>Error: {error.message}</p>
	{/await}
</div>

<pre>
    <!-- {JSON.stringify(data, null, 2)} -->
</pre>
